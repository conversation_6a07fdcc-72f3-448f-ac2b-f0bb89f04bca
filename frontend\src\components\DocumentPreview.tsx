import React, { useState, useEffect } from 'react';
import api from '../api';
import LoadingSpinner from './LoadingSpinner';
import { auth } from '../firebase';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { FaSearchMinus, FaSearchPlus, FaUndo } from 'react-icons/fa';

// Set up the worker for PDF.js
// Use HTTPS for the worker to avoid mixed content issues
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

interface DocumentPreviewProps {
  documentUrl: string;
  documentId?: number;  // Add document ID for using the new endpoint
  documentName?: string;  // Add document name for display
  onClose: () => void;
}

const DocumentPreview: React.FC<DocumentPreviewProps> = ({ documentUrl, documentId, documentName, onClose }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.2);
  const [authToken, setAuthToken] = useState<string | null>(null);

  // Get authentication token
  useEffect(() => {
    const getToken = async () => {
      try {
        const user = auth.currentUser;
        if (user) {
          const token = await user.getIdToken();
          setAuthToken(token);
        }
      } catch (error) {
        console.error('Error getting auth token:', error);
      }
    };

    getToken();
  }, []);

  useEffect(() => {
    const fetchDocument = async () => {
      try {
        setLoading(true);
        setError(null);



        // Determine which URL to use
        let fetchUrl;

        // Debug logging
        console.log('DocumentPreview props:', {
          documentId,
          documentUrl: documentUrl ? documentUrl.substring(0, 100) + '...' : 'not provided',
          documentName
        });

        // Priority 1: If we have a document ID, use the backend endpoint (most reliable)
        if (documentId && documentId > 0) {
          fetchUrl = `/documents/${documentId}/file`;
          console.log(`Using document ID endpoint: ${fetchUrl}`);
        }
        // Priority 2: If the URL is encrypted (contains base64-like characters), prefer document ID endpoint
        else if (documentUrl && documentUrl.trim() && documentUrl.includes('=') && documentUrl.length > 100) {
          console.log('URL appears to be encrypted, cannot use directly');
          setError('Cannot display document: URL is encrypted. Please use document ID instead.');
          setLoading(false);
          return;
        }
        // Priority 3: Use direct URL if it exists and is not empty
        else if (documentUrl && documentUrl.trim()) {
          fetchUrl = documentUrl;
          console.log(`Using direct URL: ${fetchUrl}`);
        }
        // If neither documentId nor documentUrl is provided, show error
        else {
          console.error('DocumentPreview error - missing data:', { documentId, documentUrl });
          setError(`No valid document URL or ID provided. DocumentID: ${documentId || 'not provided'}, URL: ${documentUrl ? 'provided but invalid' : 'not provided'}`);
          setLoading(false);
          return;
        }

        // Fetch the document as a blob
        const authUrl = fetchUrl.startsWith('/')
          ? `${fetchUrl}${fetchUrl.includes('?') ? '&' : '?'}token=${authToken || ''}`
          : fetchUrl;

        const response = await api.get(authUrl, {
          responseType: 'blob',
        });

        // Create a blob URL for the document
        const blob = new Blob([response.data], {
          type: response.headers['content-type'] || 'application/pdf'
        });
        const url = URL.createObjectURL(blob);
        setPreviewUrl(url);
      } catch (err: any) {
        console.error('Error fetching document:', err);

        let errorMessage = 'Failed to load document';
        if (err.response) {
          if (err.response.status === 404) {
            errorMessage = 'Document not found. It may have been deleted or moved.';
          } else if (err.response.status === 403) {
            errorMessage = 'Access denied. You do not have permission to view this document.';
          } else if (err.response.status === 401) {
            errorMessage = 'Authentication required. Please log in again.';
          } else {
            errorMessage = `Server error (${err.response.status}): ${err.response.data?.error || err.message}`;
          }
        } else if (err.request) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else {
          errorMessage = err.message || 'Unknown error occurred';
        }

        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchDocument();

    // Cleanup function to revoke the blob URL when the component unmounts
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [documentUrl, documentId, authToken]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-4 border-b">
          <div>
            <h2 className="text-xl font-semibold text-amspm-text">Document Preview</h2>
            {documentName && (
              <p className="text-sm text-gray-600 mt-1">Filename: {documentName}</p>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="flex-1 overflow-hidden p-4">
          {loading ? (
            <div className="h-full flex items-center justify-center">
              <LoadingSpinner message="Loading document..." fullScreen={false} />
            </div>
          ) : error ? (
            <div className="h-full flex flex-col items-center justify-center">
              <div className="text-red-500 text-center mb-4 max-w-md">
                <p className="mb-2 font-semibold">Error loading document:</p>
                <p className="mb-4">{error}</p>
                <div className="text-sm text-gray-600 bg-gray-100 p-3 rounded">
                  <p><strong>Debug Info:</strong></p>
                  <p>Document ID: {documentId || 'Not provided'}</p>
                  <p>Document URL: {documentUrl ? 'Provided' : 'Not provided'}</p>
                  {documentName && <p>Filename: {documentName}</p>}
                </div>
              </div>
            </div>
          ) : (
            <div className="h-full">
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center">
                  {numPages && (
                    <span className="text-sm text-gray-600">
                      Page {pageNumber} of {numPages}
                    </span>
                  )}
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setScale(prevScale => Math.max(prevScale - 0.2, 0.6))}
                    className="btn btn-sm btn-outline"
                    title="Zoom Out"
                  >
                    <FaSearchMinus />
                  </button>
                  <button
                    onClick={() => setScale(1.2)}
                    className="btn btn-sm btn-outline"
                    title="Reset Zoom"
                  >
                    <FaUndo />
                  </button>
                  <button
                    onClick={() => setScale(prevScale => Math.min(prevScale + 0.2, 3))}
                    className="btn btn-sm btn-outline"
                    title="Zoom In"
                  >
                    <FaSearchPlus />
                  </button>
                </div>
              </div>

              <div className="border border-gray-300 rounded overflow-auto" style={{ height: 'calc(100% - 40px)' }}>
                <Document
                  file={previewUrl || ''}
                  onLoadSuccess={({ numPages }) => {
                    setNumPages(numPages);
                  }}
                  options={{
                    // Add authentication to PDF.js requests
                    httpHeaders: {
                      'Authorization': authToken ? `Bearer ${authToken}` : ''
                    },
                    cMapUrl: 'https://unpkg.com/pdfjs-dist@3.4.120/cmaps/',
                    cMapPacked: true,
                    standardFontDataUrl: 'https://unpkg.com/pdfjs-dist@3.4.120/standard_fonts/'
                  }}
                  onLoadError={(error) => {
                    console.error('Error loading PDF:', error);
                    setError(`Failed to load PDF document: ${error.message}`);
                  }}
                  loading={
                    <div className="flex justify-center items-center h-full">
                      <div className="text-center">
                        <LoadingSpinner message="Loading PDF..." fullScreen={false} />
                        <p className="text-xs text-gray-500 mt-2">URL: {documentUrl.substring(0, 50)}...</p>
                      </div>
                    </div>
                  }
                >
                  <Page
                    pageNumber={pageNumber}
                    scale={scale}
                    renderAnnotationLayer={true}
                    renderTextLayer={true}
                  />
                </Document>
              </div>

              {numPages && numPages > 1 && (
                <div className="flex justify-between items-center mt-4">
                  <button
                    onClick={() => setPageNumber(prev => Math.max(prev - 1, 1))}
                    disabled={pageNumber <= 1}
                    className={`btn btn-sm ${pageNumber <= 1 ? 'btn-disabled' : 'btn-primary'}`}
                  >
                    Previous
                  </button>
                  <p className="text-sm">
                    Page {pageNumber} of {numPages}
                  </p>
                  <button
                    onClick={() => setPageNumber(prev => Math.min(prev + 1, numPages || 1))}
                    disabled={pageNumber >= (numPages || 1)}
                    className={`btn btn-sm ${pageNumber >= (numPages || 1) ? 'btn-disabled' : 'btn-primary'}`}
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="p-4 border-t flex justify-end">
          <button
            onClick={onClose}
            className="btn btn-outline"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default DocumentPreview;
